@echo off
echo ========================================
echo    CHUNKFORMER NUITKA BUILD (OPTIMIZED)
echo ========================================

echo 🔧 Bắt đầu build với cấu hình tối ưu...
echo ⏱️  Thời gian ước tính: 15-30 phút
echo 💾 RAM cần thiết: 4GB+
echo.

 ^
nuitka    --standalone    --enable-plugin=torch    --enable-plugin=numpy    --enable-plugin=multiprocessing    --enable-plugin=pkg-resources    --enable-plugin=anti-bloat    --include-package=torch    --include-package=torchaudio    --include-package=numpy    --include-package=scipy    --include-package=librosa    --include-package=soundfile    --include-package=pyaudio    --include-package=flask    --include-package=flask_cors    --include-package=RealtimeSTT    --include-package=speechbrain    --include-package=transformers    --include-package=datasets    --include-package=accelerate    --include-package=openpyxl    --include-package=pandas    --include-package=matplotlib    --include-package=seaborn    --include-package=plotly    --include-package=webrtcvad    --include-package=noisereduce    --include-package=pydub    --include-package=requests    --include-package=yaml    --include-package=tqdm    --include-package=pyannote    --include-data-dir=model=model    --include-data-dir=models=models    --include-data-dir=pretrained_models=pretrained_models    --include-data-dir=profile=profile    --include-data-dir=static=static    --include-data-dir=templates=templates    --include-data-dir=locales=locales    --include-data-dir=resources=resources    --include-data-dir=exports=exports    --include-data-dir=transcripts=transcripts    --include-data-dir=temp_audio_chunks=temp_audio_chunks    --include-data-dir=unknown_speakers=unknown_speakers    --include-data-file=settings.json=settings.json    --include-data-file=audio_devices_info.json=audio_devices_info.json    --include-data-file=saved_results.json=saved_results.json    --include-data-file=speaker_detection_performance.json=speaker_detection_performance.json    --include-data-file=vietnamese_corrector.py=vietnamese_corrector.py    --include-data-file=export_routes.py=export_routes.py    --include-data-file=client.js=client.js    --include-data-file=merge-speaker-transcript.js=merge-speaker-transcript.js    --windows-console-mode=attach    --windows-icon-from-ico=static/favicon.ico    --output-dir=dist    --output-filename=ChunkFormer.exe    --remove-output    --assume-yes-for-downloads    --show-progress    --show-memory    --nofollow-import-to=tkinter    --nofollow-import-to=unittest    --nofollow-import-to=test    --nofollow-import-to=tests    --nofollow-import-to=pytest    --nofollow-import-to=IPython    --nofollow-import-to=jupyter    --nofollow-import-to=notebook    --nofollow-import-to=matplotlib.tests    --nofollow-import-to=scipy.tests    --nofollow-import-to=numpy.tests    --nofollow-import-to=torch.testing    realtime_chunkformer.py

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ BUILD THÀNH CÔNG!
    echo 📁 File .exe: dist\ChunkFormer.exe
    echo 📊 Kích thước: 
    dir dist\ChunkFormer.exe
    echo.
    echo 📋 Hướng dẫn triển khai:
    echo    1. Copy thư mục dist đến máy đích
    echo    2. Đảm bảo có Visual C++ Redistributable
    echo    3. Chạy ChunkFormer.exe
    echo.
) else (
    echo.
    echo ❌ BUILD THẤT BẠI!
    echo 💡 Kiểm tra lại:
    echo    - Python dependencies đã đầy đủ chưa
    echo    - Đủ dung lượng ổ cứng chưa (cần ~5GB)
    echo    - Đủ RAM chưa (cần ~4GB)
    echo.
)

pause
