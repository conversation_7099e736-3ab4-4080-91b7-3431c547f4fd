
@echo off
echo ========================================
echo     KIỂM TRA DEPENDENCIES
echo ========================================

echo 🔍 Kiểm tra Python...
python --version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Python chưa được cài đặt!
    pause
    exit /b 1
)

echo.
echo 🔍 Kiểm tra pip...
pip --version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ pip chưa được cài đặt!
    pause
    exit /b 1
)

echo.
echo 🔍 Kiểm tra Nuitka...
nuitka --version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Nuitka chưa được cài đặt!
    echo 📦 Đang cài đặt Nuitka...
    pip install nuitka
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ Không thể cài đặt Nuitka!
        pause
        exit /b 1
    )
)

echo.
echo 🔍 Cài đặt dependencies từ requirements.txt...
if exist requirements.txt (
    pip install -r requirements.txt
    if %ERRORLEVEL% NEQ 0 (
        echo ⚠️ Một số dependencies có thể chưa được cài đặt đầy đủ
    )
) else (
    echo ⚠️ Không tìm thấy file requirements.txt
)

echo.
echo ✓ Kiểm tra hoàn thành!
echo 💡 Bây giờ bạn có thể chạy build_nuitka.bat để đóng gói ứng dụng
pause
