#!/usr/bin/env python3
"""
Script để đóng gói ứng dụng ChunkFormer thành file .exe bằng Nuitka
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_nuitka_installed():
    """Kiểm tra xem Nuitka đã được cài đặt chưa"""
    try:
        result = subprocess.run(['nuitka', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ Nuitka đã được cài đặt: {result.stdout.strip()}")
            return True
        else:
            return False
    except FileNotFoundError:
        return False

def install_nuitka():
    """Cài đặt Nuitka"""
    print("📦 Đang cài đặt Nuitka...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'nuitka'], check=True)
        print("✓ Nuitka đã được cài đặt thành công!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Lỗi khi cài đặt Nuitka: {e}")
        return False

def create_build_script():
    """Tạo script build cho Nuitka"""
    
    # Đường dẫn hiện tại
    current_dir = Path.cwd()
    
    # Tạo thư mục build nếu chưa có
    build_dir = current_dir / "build"
    build_dir.mkdir(exist_ok=True)
    
    # Tạo thư mục dist nếu chưa có
    dist_dir = current_dir / "dist"
    dist_dir.mkdir(exist_ok=True)
    
    # Script build Nuitka
    build_script = f"""
@echo off
echo ========================================
echo    ĐÓNG GÓI CHUNKFORMER BẰNG NUITKA
echo ========================================

echo 🔧 Bắt đầu quá trình build...

nuitka ^
    --standalone ^
    --onefile ^
    --enable-plugin=torch ^
    --enable-plugin=numpy ^
    --enable-plugin=multiprocessing ^
    --enable-plugin=pkg-resources ^
    --include-package=torch ^
    --include-package=torchaudio ^
    --include-package=numpy ^
    --include-package=scipy ^
    --include-package=librosa ^
    --include-package=soundfile ^
    --include-package=pyaudio ^
    --include-package=flask ^
    --include-package=RealtimeSTT ^
    --include-package=speechbrain ^
    --include-package=transformers ^
    --include-package=datasets ^
    --include-package=accelerate ^
    --include-package=openpyxl ^
    --include-package=pandas ^
    --include-package=matplotlib ^
    --include-package=seaborn ^
    --include-package=plotly ^
    --include-package=webrtcvad ^
    --include-package=noisereduce ^
    --include-package=pydub ^
    --include-package=requests ^
    --include-data-dir=model=model ^
    --include-data-dir=models=models ^
    --include-data-dir=pretrained_models=pretrained_models ^
    --include-data-dir=profile=profile ^
    --include-data-dir=static=static ^
    --include-data-dir=templates=templates ^
    --include-data-dir=locales=locales ^
    --include-data-dir=resources=resources ^
    --include-data-file=settings.json=settings.json ^
    --include-data-file=audio_devices_info.json=audio_devices_info.json ^
    --include-data-file=saved_results.json=saved_results.json ^
    --include-data-file=speaker_detection_performance.json=speaker_detection_performance.json ^
    --windows-console-mode=attach ^
    --windows-icon-from-ico=static/favicon.ico ^
    --output-dir=dist ^
    --output-filename=ChunkFormer.exe ^
    --remove-output ^
    --assume-yes-for-downloads ^
    --show-progress ^
    --show-memory ^
    realtime_chunkformer.py

if %ERRORLEVEL% EQU 0 (
    echo ✓ Build thành công!
    echo 📁 File .exe được tạo tại: dist\\ChunkFormer.exe
    echo.
    echo 📋 Hướng dẫn sử dụng:
    echo    1. Copy toàn bộ thư mục dist đến máy đích
    echo    2. Chạy ChunkFormer.exe
    echo.
    pause
) else (
    echo ❌ Build thất bại!
    echo 💡 Kiểm tra lại các dependencies và thử lại
    pause
)
"""
    
    # Lưu script build
    build_script_path = current_dir / "build_nuitka.bat"
    with open(build_script_path, 'w', encoding='utf-8') as f:
        f.write(build_script)
    
    print(f"✓ Đã tạo script build: {build_script_path}")
    return build_script_path

def create_advanced_build_script():
    """Tạo script build nâng cao với nhiều tùy chọn"""
    
    current_dir = Path.cwd()
    
    advanced_script = f"""
@echo off
echo ========================================
echo   NUITKA BUILD - CHẾ ĐỘ NÂNG CAO
echo ========================================

set /p choice="Chọn chế độ build (1=Onefile, 2=Standalone, 3=Module): "

if "%choice%"=="1" goto onefile
if "%choice%"=="2" goto standalone  
if "%choice%"=="3" goto module
goto end

:onefile
echo 🔧 Build chế độ Onefile (một file .exe duy nhất)...
nuitka ^
    --onefile ^
    --enable-plugin=torch ^
    --enable-plugin=numpy ^
    --enable-plugin=multiprocessing ^
    --include-package=torch ^
    --include-package=torchaudio ^
    --include-package=numpy ^
    --include-package=flask ^
    --include-package=RealtimeSTT ^
    --include-data-dir=model=model ^
    --include-data-dir=models=models ^
    --include-data-dir=pretrained_models=pretrained_models ^
    --include-data-dir=profile=profile ^
    --include-data-dir=static=static ^
    --include-data-dir=templates=templates ^
    --windows-console-mode=attach ^
    --output-dir=dist ^
    --output-filename=ChunkFormer_Onefile.exe ^
    --remove-output ^
    --assume-yes-for-downloads ^
    --show-progress ^
    realtime_chunkformer.py
goto end

:standalone
echo 🔧 Build chế độ Standalone (thư mục chứa .exe và dependencies)...
nuitka ^
    --standalone ^
    --enable-plugin=torch ^
    --enable-plugin=numpy ^
    --enable-plugin=multiprocessing ^
    --include-package=torch ^
    --include-package=torchaudio ^
    --include-package=numpy ^
    --include-package=flask ^
    --include-package=RealtimeSTT ^
    --include-data-dir=model=model ^
    --include-data-dir=models=models ^
    --include-data-dir=pretrained_models=pretrained_models ^
    --include-data-dir=profile=profile ^
    --include-data-dir=static=static ^
    --include-data-dir=templates=templates ^
    --windows-console-mode=attach ^
    --output-dir=dist ^
    --output-filename=ChunkFormer_Standalone.exe ^
    --remove-output ^
    --assume-yes-for-downloads ^
    --show-progress ^
    realtime_chunkformer.py
goto end

:module
echo 🔧 Build chế độ Module (tạo module Python được tối ưu)...
nuitka ^
    --module ^
    --enable-plugin=torch ^
    --enable-plugin=numpy ^
    --output-dir=dist ^
    --remove-output ^
    --show-progress ^
    realtime_chunkformer.py
goto end

:end
if %ERRORLEVEL% EQU 0 (
    echo ✓ Build hoàn thành!
    echo 📁 Kiểm tra thư mục dist để xem kết quả
) else (
    echo ❌ Build thất bại!
)
pause
"""
    
    advanced_script_path = current_dir / "build_nuitka_advanced.bat"
    with open(advanced_script_path, 'w', encoding='utf-8') as f:
        f.write(advanced_script)
    
    print(f"✓ Đã tạo script build nâng cao: {advanced_script_path}")
    return advanced_script_path

def create_requirements_check():
    """Tạo script kiểm tra requirements"""
    
    current_dir = Path.cwd()
    
    check_script = """
@echo off
echo ========================================
echo     KIỂM TRA DEPENDENCIES
echo ========================================

echo 🔍 Kiểm tra Python...
python --version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Python chưa được cài đặt!
    pause
    exit /b 1
)

echo.
echo 🔍 Kiểm tra pip...
pip --version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ pip chưa được cài đặt!
    pause
    exit /b 1
)

echo.
echo 🔍 Kiểm tra Nuitka...
nuitka --version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Nuitka chưa được cài đặt!
    echo 📦 Đang cài đặt Nuitka...
    pip install nuitka
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ Không thể cài đặt Nuitka!
        pause
        exit /b 1
    )
)

echo.
echo 🔍 Cài đặt dependencies từ requirements.txt...
if exist requirements.txt (
    pip install -r requirements.txt
    if %ERRORLEVEL% NEQ 0 (
        echo ⚠️ Một số dependencies có thể chưa được cài đặt đầy đủ
    )
) else (
    echo ⚠️ Không tìm thấy file requirements.txt
)

echo.
echo ✓ Kiểm tra hoàn thành!
echo 💡 Bây giờ bạn có thể chạy build_nuitka.bat để đóng gói ứng dụng
pause
"""
    
    check_script_path = current_dir / "check_requirements.bat"
    with open(check_script_path, 'w', encoding='utf-8') as f:
        f.write(check_script)
    
    print(f"✓ Đã tạo script kiểm tra: {check_script_path}")
    return check_script_path

def main():
    """Hàm chính"""
    print("🚀 CHUNKFORMER NUITKA BUILD SETUP")
    print("=" * 50)
    
    # Kiểm tra Nuitka
    if not check_nuitka_installed():
        print("❌ Nuitka chưa được cài đặt")
        install_choice = input("📦 Bạn có muốn cài đặt Nuitka không? (y/n): ")
        if install_choice.lower() in ['y', 'yes']:
            if not install_nuitka():
                print("❌ Không thể tiếp tục mà không có Nuitka")
                return
        else:
            print("💡 Vui lòng cài đặt Nuitka trước: pip install nuitka")
            return
    
    # Tạo các script
    print("\n📝 Tạo các script build...")
    create_requirements_check()
    create_build_script()
    create_advanced_build_script()
    
    print("\n✅ SETUP HOÀN THÀNH!")
    print("\n📋 Các bước tiếp theo:")
    print("1. Chạy check_requirements.bat để kiểm tra dependencies")
    print("2. Chạy build_nuitka.bat để build cơ bản")
    print("3. Hoặc chạy build_nuitka_advanced.bat để có nhiều tùy chọn")
    print("\n💡 Lưu ý:")
    print("- Quá trình build có thể mất 10-30 phút")
    print("- Cần ít nhất 4GB RAM trống")
    print("- File .exe sẽ được tạo trong thư mục dist/")

if __name__ == "__main__":
    main()
