
@echo off
echo ========================================
echo    ĐÓNG GÓI CHUNKFORMER BẰNG NUITKA
echo ========================================

echo 🔧 Bắt đầu quá trình build...

nuitka ^
    --standalone ^
    --onefile ^
    --enable-plugin=torch ^
    --enable-plugin=numpy ^
    --enable-plugin=multiprocessing ^
    --enable-plugin=pkg-resources ^
    --include-package=torch ^
    --include-package=torchaudio ^
    --include-package=numpy ^
    --include-package=scipy ^
    --include-package=librosa ^
    --include-package=soundfile ^
    --include-package=pyaudio ^
    --include-package=flask ^
    --include-package=RealtimeSTT ^
    --include-package=speechbrain ^
    --include-package=transformers ^
    --include-package=datasets ^
    --include-package=accelerate ^
    --include-package=openpyxl ^
    --include-package=pandas ^
    --include-package=matplotlib ^
    --include-package=seaborn ^
    --include-package=plotly ^
    --include-package=webrtcvad ^
    --include-package=noisereduce ^
    --include-package=pydub ^
    --include-package=requests ^
    --include-data-dir=model=model ^
    --include-data-dir=models=models ^
    --include-data-dir=pretrained_models=pretrained_models ^
    --include-data-dir=profile=profile ^
    --include-data-dir=static=static ^
    --include-data-dir=templates=templates ^
    --include-data-dir=locales=locales ^
    --include-data-dir=resources=resources ^
    --include-data-file=settings.json=settings.json ^
    --include-data-file=audio_devices_info.json=audio_devices_info.json ^
    --include-data-file=saved_results.json=saved_results.json ^
    --include-data-file=speaker_detection_performance.json=speaker_detection_performance.json ^
    --windows-console-mode=attach ^
    --windows-icon-from-ico=static/favicon.ico ^
    --output-dir=dist ^
    --output-filename=ChunkFormer.exe ^
    --remove-output ^
    --assume-yes-for-downloads ^
    --show-progress ^
    --show-memory ^
    realtime_chunkformer.py

if %ERRORLEVEL% EQU 0 (
    echo ✓ Build thành công!
    echo 📁 File .exe được tạo tại: dist\ChunkFormer.exe
    echo.
    echo 📋 Hướng dẫn sử dụng:
    echo    1. Copy toàn bộ thư mục dist đến máy đích
    echo    2. Chạy ChunkFormer.exe
    echo.
    pause
) else (
    echo ❌ Build thất bại!
    echo 💡 Kiểm tra lại các dependencies và thử lại
    pause
)
