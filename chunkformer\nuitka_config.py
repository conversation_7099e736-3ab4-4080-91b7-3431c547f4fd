"""
<PERSON><PERSON><PERSON> hình <PERSON> cho ChunkFormer
<PERSON><PERSON><PERSON>u hóa quá trình build và giảm kích thước file .exe
"""

# <PERSON><PERSON><PERSON> hình cơ bản
NUITKA_CONFIG = {
    # <PERSON><PERSON> độ build
    "standalone": True,
    "onefile": False,  # Đặt True nếu muốn tạo file .exe duy nhất
    
    # Tối ưu hóa
    "optimization": {
        "enable_console": True,
        "remove_output": True,
        "assume_yes_for_downloads": True,
        "show_progress": True,
        "show_memory": True,
    },
    
    # Plugins cần thiết
    "plugins": [
        "torch",
        "numpy", 
        "multiprocessing",
        "pkg-resources",
        "anti-bloat",  # <PERSON><PERSON><PERSON><PERSON> kích thước
    ],
    
    # Packages cần include
    "include_packages": [
        "torch",
        "torchaudio", 
        "numpy",
        "scipy",
        "librosa",
        "soundfile",
        "pyaudio",
        "flask",
        "flask_cors",
        "RealtimeSTT",
        "speechbrain",
        "transformers",
        "datasets",
        "accelerate",
        "openpyxl",
        "pandas",
        "matplotlib",
        "seaborn",
        "plotly",
        "webrtcvad",
        "noisereduce",
        "pydub",
        "requests",
        "yaml",
        "tqdm",
        "pyannote",
    ],
    
    # Data directories cần include
    "include_data_dirs": [
        "model",
        "models", 
        "pretrained_models",
        "profile",
        "static",
        "templates",
        "locales",
        "resources",
        "exports",
        "transcripts",
        "temp_audio_chunks",
        "unknown_speakers",
    ],
    
    # Data files cần include
    "include_data_files": [
        "settings.json",
        "audio_devices_info.json", 
        "saved_results.json",
        "speaker_detection_performance.json",
        "vietnamese_corrector.py",
        "export_routes.py",
        "client.js",
        "merge-speaker-transcript.js",
    ],
    
    # Windows specific
    "windows": {
        "console_mode": "attach",
        "icon": "static/favicon.ico",
        "version_info": {
            "file_version": "*******",
            "product_version": "*******", 
            "file_description": "ChunkFormer Speech Recognition",
            "product_name": "ChunkFormer",
            "company_name": "ChunkFormer Team",
            "copyright": "Copyright (C) 2024",
        }
    },
    
    # Output
    "output": {
        "dir": "dist",
        "filename": "ChunkFormer.exe",
    },
    
    # Exclusions để giảm kích thước
    "nofollow_imports": [
        "tkinter",
        "unittest", 
        "test",
        "tests",
        "pytest",
        "IPython",
        "jupyter",
        "notebook",
        "matplotlib.tests",
        "scipy.tests",
        "numpy.tests",
        "torch.testing",
    ],
    
    # Anti-bloat patterns
    "anti_bloat": [
        "matplotlib:remove_unused_backends",
        "scipy:remove_tests", 
        "numpy:remove_tests",
        "torch:remove_tests",
        "transformers:remove_tests",
    ]
}

def generate_nuitka_command(config=None):
    """Tạo command line cho Nuitka từ config"""
    if config is None:
        config = NUITKA_CONFIG
    
    cmd = ["nuitka"]
    
    # Chế độ build
    if config.get("standalone"):
        cmd.append("--standalone")
    if config.get("onefile"):
        cmd.append("--onefile")
    
    # Plugins
    for plugin in config.get("plugins", []):
        cmd.append(f"--enable-plugin={plugin}")
    
    # Include packages
    for package in config.get("include_packages", []):
        cmd.append(f"--include-package={package}")
    
    # Include data dirs
    for data_dir in config.get("include_data_dirs", []):
        cmd.append(f"--include-data-dir={data_dir}={data_dir}")
    
    # Include data files
    for data_file in config.get("include_data_files", []):
        cmd.append(f"--include-data-file={data_file}={data_file}")
    
    # Windows settings
    windows_config = config.get("windows", {})
    if windows_config.get("console_mode"):
        cmd.append(f"--windows-console-mode={windows_config['console_mode']}")
    if windows_config.get("icon"):
        cmd.append(f"--windows-icon-from-ico={windows_config['icon']}")
    
    # Output settings
    output_config = config.get("output", {})
    if output_config.get("dir"):
        cmd.append(f"--output-dir={output_config['dir']}")
    if output_config.get("filename"):
        cmd.append(f"--output-filename={output_config['filename']}")
    
    # Optimization settings
    opt_config = config.get("optimization", {})
    if opt_config.get("remove_output"):
        cmd.append("--remove-output")
    if opt_config.get("assume_yes_for_downloads"):
        cmd.append("--assume-yes-for-downloads")
    if opt_config.get("show_progress"):
        cmd.append("--show-progress")
    if opt_config.get("show_memory"):
        cmd.append("--show-memory")
    
    # Nofollow imports
    for import_name in config.get("nofollow_imports", []):
        cmd.append(f"--nofollow-import-to={import_name}")
    
    # Main file
    cmd.append("realtime_chunkformer.py")
    
    return cmd

def save_build_script():
    """Lưu script build với cấu hình tối ưu"""
    cmd = generate_nuitka_command()
    
    # Tạo batch script
    batch_content = f"""@echo off
echo ========================================
echo    CHUNKFORMER NUITKA BUILD (OPTIMIZED)
echo ========================================

echo 🔧 Bắt đầu build với cấu hình tối ưu...
echo ⏱️  Thời gian ước tính: 15-30 phút
echo 💾 RAM cần thiết: 4GB+
echo.

{' ^' + chr(10) + '    '.join(cmd)}

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ BUILD THÀNH CÔNG!
    echo 📁 File .exe: dist\\ChunkFormer.exe
    echo 📊 Kích thước: 
    dir dist\\ChunkFormer.exe
    echo.
    echo 📋 Hướng dẫn triển khai:
    echo    1. Copy thư mục dist đến máy đích
    echo    2. Đảm bảo có Visual C++ Redistributable
    echo    3. Chạy ChunkFormer.exe
    echo.
) else (
    echo.
    echo ❌ BUILD THẤT BẠI!
    echo 💡 Kiểm tra lại:
    echo    - Python dependencies đã đầy đủ chưa
    echo    - Đủ dung lượng ổ cứng chưa (cần ~5GB)
    echo    - Đủ RAM chưa (cần ~4GB)
    echo.
)

pause
"""
    
    with open("build_optimized.bat", "w", encoding="utf-8") as f:
        f.write(batch_content)
    
    print("✓ Đã tạo script build tối ưu: build_optimized.bat")

if __name__ == "__main__":
    save_build_script()
    print("🔧 Cấu hình Nuitka đã được tạo!")
    print("📝 Chạy build_optimized.bat để build với cấu hình tối ưu")
