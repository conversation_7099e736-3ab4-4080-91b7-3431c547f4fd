@echo off
chcp 65001 >nul
echo ========================================
echo     CHUNKFORMER - TEST BUILD
echo ========================================

echo 🧪 Đây là bản test build đơn giản
echo ⚡ Chỉ build các components cơ bản nhất
echo ⏱️  Thời gian: 5-10 phút
echo.

:: Kiểm tra Python
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Python chưa được cài đặt!
    pause
    exit /b 1
)

:: Kiểm tra Nuitka
nuitka --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Nuitka chưa được cài đặt!
    echo 📦 Đang cài đặt...
    pip install nuitka
)

:: Tạo thư mục test
if not exist "test_dist" mkdir test_dist

echo 🚀 Bắt đầu test build...

:: Build command tối giản
nuitka ^
    --standalone ^
    --enable-plugin=numpy ^
    --include-package=numpy ^
    --include-package=torch ^
    --include-data-file=settings.json=settings.json ^
    --windows-console-mode=attach ^
    --output-dir=test_dist ^
    --output-filename=ChunkFormer_Test.exe ^
    --remove-output ^
    --assume-yes-for-downloads ^
    --show-progress ^
    realtime_chunkformer.py

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ TEST BUILD THÀNH CÔNG!
    echo 📁 File test: test_dist\ChunkFormer_Test.exe
    echo.
    echo 💡 Đây chỉ là bản test, có thể thiếu một số tính năng
    echo 🔄 Để build đầy đủ, hãy chạy quick_build.bat
    echo.
) else (
    echo.
    echo ❌ TEST BUILD THẤT BẠI!
    echo 💡 Hãy thử quick_build.bat thay thế
    echo.
)

pause
