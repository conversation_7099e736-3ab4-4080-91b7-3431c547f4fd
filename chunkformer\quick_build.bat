@echo off
chcp 65001 >nul
echo ========================================
echo     CHUNKFORMER - QUICK BUILD
echo ========================================

echo 🔍 Kiểm tra môi trường...

:: Kiểm tra Python
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Python chưa được cài đặt hoặc không có trong PATH!
    echo 💡 Vui lòng cài đặt Python từ https://python.org
    pause
    exit /b 1
)

:: Kiểm tra Nuitka
nuitka --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️  Nuitka chưa được cài đặt
    echo 📦 Đang cài đặt Nuitka...
    pip install nuitka
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ Không thể cài đặt Nuitka!
        pause
        exit /b 1
    )
    echo ✅ Nuitka đã được cài đặt
)

:: Cài đặt dependencies cơ bản
echo 📦 Cài đặt dependencies cần thiết...
pip install torch torchaudio numpy flask RealtimeSTT speechbrain transformers openpyxl pandas

:: Tạo thư mục output
if not exist "dist" mkdir dist

echo.
echo 🚀 Bắt đầu build...
echo ⏱️  Thời gian ước tính: 10-20 phút
echo.

:: Build command đơn giản
nuitka ^
    --standalone ^
    --enable-plugin=torch ^
    --enable-plugin=numpy ^
    --include-package=torch ^
    --include-package=torchaudio ^
    --include-package=numpy ^
    --include-package=flask ^
    --include-package=RealtimeSTT ^
    --include-data-dir=model=model ^
    --include-data-dir=models=models ^
    --include-data-dir=static=static ^
    --include-data-dir=templates=templates ^
    --include-data-file=settings.json=settings.json ^
    --windows-console-mode=attach ^
    --output-dir=dist ^
    --output-filename=ChunkFormer.exe ^
    --remove-output ^
    --assume-yes-for-downloads ^
    --show-progress ^
    realtime_chunkformer.py

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ BUILD THÀNH CÔNG!
    echo 📁 File executable: dist\ChunkFormer.exe
    echo.
    echo 🎉 Hoàn thành! Bạn có thể chạy ứng dụng từ thư mục dist
    echo.
    set /p run_choice="Bạn có muốn chạy thử ứng dụng không? (y/n): "
    if /i "%run_choice%"=="y" (
        cd dist
        ChunkFormer.exe
    )
) else (
    echo.
    echo ❌ BUILD THẤT BẠI!
    echo 💡 Thử các giải pháp sau:
    echo    1. Kiểm tra lại dependencies: pip install -r requirements.txt
    echo    2. Đảm bảo đủ dung lượng ổ cứng (cần ~3GB)
    echo    3. Đảm bảo đủ RAM (cần ~4GB)
    echo    4. Chạy lại với quyền Administrator
    echo.
)

pause
