
@echo off
echo ========================================
echo   NUITKA BUILD - CHẾ ĐỘ NÂNG CAO
echo ========================================

set /p choice="Chọn chế độ build (1=Onefile, 2=Standalone, 3=Module): "

if "%choice%"=="1" goto onefile
if "%choice%"=="2" goto standalone  
if "%choice%"=="3" goto module
goto end

:onefile
echo 🔧 Build chế độ Onefile (một file .exe duy nhất)...
nuitka ^
    --onefile ^
    --enable-plugin=torch ^
    --enable-plugin=numpy ^
    --enable-plugin=multiprocessing ^
    --include-package=torch ^
    --include-package=torchaudio ^
    --include-package=numpy ^
    --include-package=flask ^
    --include-package=RealtimeSTT ^
    --include-data-dir=model=model ^
    --include-data-dir=models=models ^
    --include-data-dir=pretrained_models=pretrained_models ^
    --include-data-dir=profile=profile ^
    --include-data-dir=static=static ^
    --include-data-dir=templates=templates ^
    --windows-console-mode=attach ^
    --output-dir=dist ^
    --output-filename=ChunkFormer_Onefile.exe ^
    --remove-output ^
    --assume-yes-for-downloads ^
    --show-progress ^
    realtime_chunkformer.py
goto end

:standalone
echo 🔧 Build chế độ Standalone (thư mục chứa .exe và dependencies)...
nuitka ^
    --standalone ^
    --enable-plugin=torch ^
    --enable-plugin=numpy ^
    --enable-plugin=multiprocessing ^
    --include-package=torch ^
    --include-package=torchaudio ^
    --include-package=numpy ^
    --include-package=flask ^
    --include-package=RealtimeSTT ^
    --include-data-dir=model=model ^
    --include-data-dir=models=models ^
    --include-data-dir=pretrained_models=pretrained_models ^
    --include-data-dir=profile=profile ^
    --include-data-dir=static=static ^
    --include-data-dir=templates=templates ^
    --windows-console-mode=attach ^
    --output-dir=dist ^
    --output-filename=ChunkFormer_Standalone.exe ^
    --remove-output ^
    --assume-yes-for-downloads ^
    --show-progress ^
    realtime_chunkformer.py
goto end

:module
echo 🔧 Build chế độ Module (tạo module Python được tối ưu)...
nuitka ^
    --module ^
    --enable-plugin=torch ^
    --enable-plugin=numpy ^
    --output-dir=dist ^
    --remove-output ^
    --show-progress ^
    realtime_chunkformer.py
goto end

:end
if %ERRORLEVEL% EQU 0 (
    echo ✓ Build hoàn thành!
    echo 📁 Kiểm tra thư mục dist để xem kết quả
) else (
    echo ❌ Build thất bại!
)
pause
