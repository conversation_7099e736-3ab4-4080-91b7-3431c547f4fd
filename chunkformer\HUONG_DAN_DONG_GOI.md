# 🚀 HƯỚNG DẪN ĐÓNG GÓI CHUNKFORMER THÀNH FILE .EXE

## 📋 Tổng quan
Tài liệu này hướng dẫn cách sử dụng Nuitka để đóng gói ứng dụng ChunkFormer thành file .exe có thể chạy độc lập trên Windows.

## 🔧 Yêu cầu hệ thống
- **Windows 10/11** (64-bit)
- **Python 3.8+** đã được cài đặt
- **RAM**: Tối thiểu 8GB (khuyến nghị 16GB)
- **Ổ cứng**: Ít nhất 10GB dung lượng trống
- **Visual Studio Build Tools** hoặc **Visual Studio Community**

## 📦 Các file đã được tạo

### Scripts chính:
- `quick_build.bat` - Build nhanh (khuyến nghị cho lần đầu)
- `build_optimized.bat` - Build với cấu hình tối ưu
- `build_nuitka.bat` - Build cơ bản
- `build_nuitka_advanced.bat` - Build với nhiều tùy chọn
- `check_requirements.bat` - Kiểm tra dependencies

### Files cấu hình:
- `requirements.txt` - Danh sách dependencies
- `nuitka_config.py` - Cấu hình Nuitka
- `build_exe.py` - Script setup tự động

## 🚀 Cách sử dụng

### Phương pháp 1: Build nhanh (Khuyến nghị)
```bash
# Mở Command Prompt hoặc PowerShell tại thư mục chunkformer
# Chạy lệnh:
quick_build.bat
```

### Phương pháp 2: Build từng bước
```bash
# Bước 1: Kiểm tra dependencies
check_requirements.bat

# Bước 2: Build ứng dụng
build_optimized.bat
```

### Phương pháp 3: Build nâng cao
```bash
# Chạy script với nhiều tùy chọn
build_nuitka_advanced.bat
```

## ⚙️ Các chế độ build

### 1. Onefile Mode
- Tạo **1 file .exe duy nhất**
- Kích thước lớn (~500MB-1GB)
- Khởi động chậm hơn
- Dễ phân phối

### 2. Standalone Mode  
- Tạo **thư mục chứa .exe và dependencies**
- Kích thước nhỏ hơn
- Khởi động nhanh hơn
- Cần copy cả thư mục

### 3. Module Mode
- Tạo **module Python được tối ưu**
- Dành cho developers

## 📊 Thời gian và tài nguyên

| Chế độ | Thời gian | RAM cần | Kích thước output |
|--------|-----------|---------|-------------------|
| Quick Build | 10-15 phút | 4GB | ~300MB |
| Optimized | 15-25 phút | 6GB | ~200MB |
| Onefile | 20-30 phút | 8GB | ~500MB |

## 🔍 Troubleshooting

### Lỗi thường gặp:

#### 1. "Nuitka not found"
```bash
pip install nuitka
```

#### 2. "Visual Studio Build Tools required"
- Tải và cài đặt Visual Studio Build Tools
- Hoặc cài Visual Studio Community

#### 3. "Out of memory"
- Đóng các ứng dụng khác
- Tăng Virtual Memory
- Sử dụng chế độ build nhẹ hơn

#### 4. "Module not found"
```bash
pip install -r requirements.txt
```

#### 5. Build thất bại
- Kiểm tra đường dẫn không có ký tự đặc biệt
- Chạy với quyền Administrator
- Đảm bảo đủ dung lượng ổ cứng

## 📁 Cấu trúc output

Sau khi build thành công:
```
dist/
├── ChunkFormer.exe          # File chính
├── model/                   # Models AI
├── static/                  # Web assets
├── templates/               # HTML templates
├── profile/                 # Speaker profiles
└── [các dependencies khác]
```

## 🎯 Tối ưu hóa

### Giảm kích thước:
1. Sử dụng `build_optimized.bat`
2. Xóa các model không cần thiết
3. Nén bằng UPX (tùy chọn)

### Tăng tốc độ:
1. Sử dụng Standalone mode
2. Đặt trên SSD
3. Tắt antivirus tạm thời khi build

## 📋 Checklist trước khi build

- [ ] Python đã được cài đặt
- [ ] Nuitka đã được cài đặt
- [ ] Visual Studio Build Tools có sẵn
- [ ] Đủ RAM và dung lượng ổ cứng
- [ ] Tất cả dependencies đã được cài
- [ ] Đường dẫn không có ký tự đặc biệt
- [ ] Antivirus đã được tắt tạm thời

## 🚀 Triển khai

### Trên máy đích:
1. Copy thư mục `dist/` 
2. Cài đặt Visual C++ Redistributable (nếu cần)
3. Chạy `ChunkFormer.exe`

### Lưu ý:
- Lần chạy đầu có thể chậm (khởi tạo models)
- Cần kết nối internet để tải models (nếu chưa có)
- Microphone permissions cần được cấp

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra log trong terminal
2. Thử build với chế độ khác
3. Đảm bảo dependencies đầy đủ
4. Kiểm tra system requirements

## 🎉 Hoàn thành!

Sau khi build thành công, bạn sẽ có file .exe có thể:
- Chạy độc lập trên Windows
- Nhận dạng giọng nói real-time
- Phân biệt người nói
- Xuất kết quả ra nhiều định dạng

**Chúc bạn thành công! 🎊**
